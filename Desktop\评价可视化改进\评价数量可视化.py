#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
堆堆袜差评 - 质量问题关键词提取 & 可视化

"""

import re
import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import json  # 添加json库，用于解析商品规格

# 可选：import jieba  # 若想自行扩充关键词，可开启分词功能
plt.rcParams["font.sans-serif"] = ["SimHei"]  # 黑体；或改成 "Microsoft YaHei"
plt.rcParams["axes.unicode_minus"] = False  # 正常显示负号
###############################################################################
# ⚙️ 配 置
###############################################################################
INPUT_FILE = r"1.xlsx"  # 使用当前目录下存在的文件
# 备选路径：r"F:\李泽鹏\获取数据\商品评论\2049\合并商品评论_20250515132849.xlsx"

QUALITY_KWS = [
                  '起球', '掉毛', '脱线', '抽丝', '易破', '容易烂', '线头多', '编织稀疏', '不结实', '掉色', '掉皮',
                  '勾丝', '撕裂', '开线', '散边', '缩水', '变形', '掉絮', '异味', '发霉', '起泡', '褪色', '炸毛',

                  '薄','破', '不够厚', '不够密', '透肉', '透光', '显瘦效果差', '不保暖','长短','尺寸小'
                  '不加厚', '像夏天袜子', '冬季不抗冻', '冬天不适用', '保暖效果差', '冷死了', '不御寒',

                  '色差大', '颜色不对', '实物与图片不符', '实物比图片暗', '实物更丑', '盗图宣传', '卖家秀买家秀',
                  '灰显绿', '白显黄', '黑不够黑', '发白', '颜色不正', '颜色偏', '色差严重', '奶白变绿', '色差离谱',

                  '尺码不准', '太小', '太瘦', '太紧', '不合脚', '不贴身', '弹力差', '没弹性', '回弹差', '松紧带勒腿',
                  '松紧带松垮', '袜口太窄', '袜口太宽', '长度不够', '太短', '不到膝盖', '不到大腿', '长度偏短',
                  '缩起来',

                  '滑落', '往下掉', '卷边', '翻边', '塌陷', '皱巴巴', '移位', '堆在一起', '不跟脚', '掉跟',
                  '不贴合腿部',
                  '走路滑', '穿不住', '走动不适', '不舒适', '勒腿', '扎人', '痒', '不亲肤', '粗糙',

                  '客服态度差', '已读不回', '不理人', '敷衍', '处理慢', '推诿责任', '不解决问题', '售后差',
                  '不退换', '退货难', '运费贵', '平台介入才解决', '客服机器人', '回复慢', '态度恶劣',

                  '发货慢', '快递慢', '物流慢', '运输慢', '到货晚', '等太久', '延误', '虚假发货', '派送不及时',
                  '暴力快递', '包装简陋', '包裹破损', '没有发票', '无标签', '无包装袋', '包装脏乱',

                  '性价比低', '不划算', '买亏了', '后悔买了',
                  '不值得回购', '不值得买', '差评警告','脏','难闻','味道'

                  '发错货', '颜色发错', '数量发错', '少发', '漏发', '补发难', '换货难', '恶意差评引导',
                  '刷单刷评', '虚假宣传', '描述不符', '图文不符', '实物不符', '主图误导', '详情页虚假', '夸大宣传',

                  '做工粗糙', '材质差', '面料差', '用料差', '缝线差', '缝制不牢', '缝线不整齐', '褶皱多', '有污渍',
                  '线头外露', '针脚杂乱', '次品出厂', '二次销售嫌疑', '看起来旧', '非全新', '疑似二手',

                  '不吸汗', '不透气', '闷热', '臭脚', '脚汗严重', '出汗多', '不排汗', '闷得慌', '不顺滑',
                  '不防滑', '打滑', '摩擦力差', '走路不舒服', '脚趾顶破', '脚感差', '穿久难受',

                  '不拉长', '不能堆堆', '堆不起来', '显腿粗', '不修饰腿型', '显胖', '腿围太大穿不了',
                  '小腿围限制', '不适合胖人', '勒肉', '显腿短', '显腿粗', '穿搭尴尬', '不百搭',

                  '不耐用', '寿命短', '一穿就坏', '穿几天破洞', '一洗就坏', '一拉就断', '一碰就破', '不结实',
                  '廉价感强', '不耐洗', '不耐磨', '不牢固',

                  '商品已下架', '联系不上商家', '店铺跑路', '售后无门', '投诉无门', '无人跟进', '问题未解决',
                  '退款难', '维权难', '霸王条款', '退货要剪标签', '强制好评', '诱导好评', '好评返现被删',

                  # 质量类差评关键词
                  '做工粗糙', '面料差', '材质差', '掉毛', '脱线', '抽丝',
                  '易破', '容易烂', '起球', '勾丝', '撕裂', '线头多', '编织稀疏', '不结实', '容易掉',
                  '容易起毛球', '容易断线', '容易变形', '容易抽线', '容易脱落', '容易开线', '容易散边','没有压力','不是微压','没压力'

                  # 厚度/保暖性差评关键词
                  '薄如蝉翼', '薄得离谱', '不厚实', '不加厚', '不够厚', '不够密',
                  '不够紧实', '不够扎实', '不够保暖', '透肉', '透光', '透肤色', '透腿', '不挡肉', '保暖效果差',
                  '冬天不抗冻', '不适合冬天穿', '根本不保暖', '不防寒', '不适合寒冷天气',

                  # 颜色与实物不符类差评关键词
                  '色差大', '实物颜色不对', '和图片颜色不一样', '灰色发绿', '白色偏黄', '黑色不够黑', '发白',
                  '颜色不正', '颜色不对', '颜色不均匀', '颜色太亮', '颜色太暗', '图片误导', '有色差', '实物更丑',
                  '实物显廉价', '与描述不符', '实物和详情页不一样', '主图与实物差距大', '商家盗图宣传', '卖家秀买家秀',
                  '照骗', '商品照骗', '图文不符',

                  # 尺寸/长度/版型差评关键词
                  '尺码不准', '太小', '太瘦', '太紧', '不合脚', '不贴身', '不弹力', '弹力差', '不回弹',
                  '松紧带太紧', '松紧带勒腿', '松紧带松垮', '松紧带没弹性', '袜口太窄', '袜口太宽', '不包裹',
                  '包不住', '长度不够', '太短', '短到膝盖上一点点', '刚到膝盖', '根本不到大腿', '不到预期长度',
                  '松垮', '宽度不合适', '松松垮垮', '不服帖', '不修身', '不显瘦', '反而显胖', '显腿粗',
                  '不修饰腿型', '不显瘦', '不遮腿', '不拉长腿型',

                  # 穿着体验类差评关键词
                  '不舒服', '不亲肤', '不柔软', '扎人', '痒', '勒腿', '穿着不舒服', '穿起来难受', '不透气',
                  '臭脚', '出汗闷热', '脚部过敏', '不吸汗', '不排汗', '不顺滑', '滑落', '下滑', '往下滑',
                  '往下掉', '容易掉', '容易卷边', '容易翻边', '容易塌陷', '容易皱', '容易堆在一起', '容易移位',
                  '容易脱线', '不跟脚', '掉跟', '掉袜子', '不贴合腿部', '不贴合小腿', '不稳定', '不好穿',
                  '难穿进', '难脱下', '穿脱困难', '不方便活动', '行动受限',

                  # 服务类差评关键词
                  '客服态度差', '客服回复慢', '客服不理人', '客服已读不回', '客服敷衍', '不解决问题', '不处理售后',
                  '客服不专业', '客服推诿责任', '发错货', '颜色发错', '数量发错', '发错尺码', '发错款式', '漏发',
                  '少发', '发货慢', '快递慢', '快递暴力', '快递破损', '包装简陋', '包装破损', '没有发票',
                  '没有说明书', '没有标签', '没有包装袋', '没有售后服务', '不退换', '退货难', '退货运费贵',
                  '不包退换', '处理效率低', '不补偿', '不补发', '平台介入才解决', '商家不配合',

                  # 性价比/价格类差评关键词
                  '性价比低', '性价比不高', '贵了', '买贵了',
                  '不值得买', '不值得回购', '不划算', '几块钱都不值', '买亏了',
                  '后悔买了', '差评给商家', '给五星差评', '差评警告', '诈骗',
                  '不诚信', '不靠谱', '不良心', '不推荐这家店', '不再回购',

                  # 其他负面表达
                  '差评警告', '差评给店铺', '差评给客服',
                  '刷单刷评', '虚假宣传', '虚假描述', '描述不符', '严重不符', '图文不符',
                  '实物与商品页不符', '商家欺骗消费者', '商家不负责任', '商家不讲信用', '商家无良', '商家垃圾',
                  '商家差评', '店铺差评', '商家不诚信', '商家不靠谱', '商家坑人', '商家太差劲', '商家太垃圾',
                  '商家太差评', '商家太无语', '商家太敷衍', '商家太敷衍了事'
              ]


###############################################################################
# 🏗️  主  逻  辑
###############################################################################
def load_reviews(file_path: str) -> pd.DataFrame:
    """读取 CSV 或 Excel 并返回 1-3 星有效文字评论 DataFrame"""
    if file_path.lower().endswith('.xlsx'):
        df = pd.read_excel(file_path)  # 读取 Excel 文件
    else:
        df = pd.read_csv(file_path, encoding="utf-8")  # 读取 CSV 文件
    df = df[df["评分"] <= 9999999]  # 限 1-3 星
    # 处理评论内容
    df["评论内容"] = (
        df["评论内容"]
        .fillna("")
        .astype(str)
        .str.strip()
        .replace("该用户未填写文字评价", "", regex=False)
    )
    # 只保留有评论内容的行
    df = df[df["评论内容"] != ""]
    return df


def count_keywords_by_product(df: pd.DataFrame, keywords: list[str]) -> dict:
    """按商品编码统计关键词出现次数"""
    # 首先检查常见的商品编码列名
    potential_columns = {
        'Q': ['Q', 'q', '商品编码', '商品编号', '产品编码', '产品编号', '商品代码', '产品代码', 'product_code',
              'item_code'],
        'R': ['R', 'r', '商品型号', '型号', '款式编码', '款式', 'model', 'style_code'],
        'S': ['S', 's',  '规格', '商品款式', 'specification', 'spec']#'商品规格'
    }

    # 查找可能的商品编码列
    code_columns = []
    column_mapping = {}  # 用于存储列名映射

    # 打印所有列名，帮助识别
    print(f"数据框所有列名: {df.columns.tolist()}")

    # 遍历每个潜在的列名类型
    for key, potential_names in potential_columns.items():
        for name in potential_names:
            # 检查列名是否存在（考虑大小写不敏感和部分匹配）
            for col in df.columns:
                if (isinstance(col, str) and
                        (col.lower() == name.lower() or
                         name.lower() in col.lower())):
                    print(f"找到商品编码列: {col} (对应 {key})")
                    code_columns.append(col)
                    column_mapping[col] = key
                    break

    # 如果找不到预定义的列，尝试查找包含"编码"、"编号"等关键词的列
    if not code_columns:
        code_keywords = ['编码', '编号', 'code', '型号', '款式', '规格']
        for col in df.columns:
            if isinstance(col, str):
                for keyword in code_keywords:
                    if keyword in col.lower():
                        print(f"从列名中识别商品编码列: {col}")
                        code_columns.append(col)
                        break

    print(f"找到的商品编码列: {code_columns}")

    # 解析JSON格式的商品规格
    for i, col in enumerate(code_columns.copy()):  # 使用copy避免在循环中修改列表
        # 检查是否需要解析JSON
        if df[col].dtype == 'object':
            # 取一个样本检查是否为JSON格式
            sample = df[col].dropna().iloc[0] if not df[col].dropna().empty else ""
            if isinstance(sample, str) and sample.startswith('[{') and sample.endswith('}]'):
                print(f"发现JSON格式的商品规格列: {col}")
                try:
                    # 创建一个新列用于存储规格值
                    new_col_name = f"{col}_parsed"
                    df[new_col_name] = df[col].apply(
                        lambda x: extract_spec_value(x) if isinstance(x, str) else None
                    )
                    print(f"成功解析JSON，创建新列: {new_col_name}")

                    # 添加新列到编码列列表
                    code_columns.append(new_col_name)
                    print(f"更新后的商品编码列: {code_columns}")
                except Exception as e:
                    print(f"解析JSON时发生错误: {e}")

    # 去除重复的列
    code_columns = list(set(code_columns))

    print(f"最终使用的商品编码列: {code_columns}")

    if not code_columns:
        print("❗ 未找到商品编码列，将返回整体统计结果")
        # 如果没有商品编码列，则返回整体统计结果
        return {"总体": count_keywords_overall(df["评论内容"], keywords)}

    # 按商品编码分组统计
    product_keywords = {}
    for col in code_columns:
        # 检查此列是否包含有效数据
        non_empty_values = df[col].dropna()
        if len(non_empty_values) == 0:
            print(f"列 {col} 不包含有效数据，跳过")
            continue

        # 获取该列的唯一商品编码（排除空值）
        product_codes = df[col].dropna().unique()
        print(f"列 {col} 中找到的唯一商品编码数量: {len(product_codes)}")

        # 显示样本数据
        if len(product_codes) > 0:
            samples = [str(code) for code in product_codes[:5]]
            print(f"示例商品编码: {samples}")

        for code in product_codes:
            if pd.isna(code) or str(code).strip() == "":
                continue

            # 获取对应商品编码的评论
            product_reviews = df[df[col] == code]["评论内容"]
            if not product_reviews.empty:
                code_str = str(code)
                # 统计该商品的关键词频次
                counts = count_keywords_overall(product_reviews, keywords)
                if counts:  # 只添加有关键词的商品
                    # 如果一个商品有多个编码，将它们标记为"编码A | 编码B"
                    if code_str in product_keywords:
                        for kw, count in counts.items():
                            if kw in product_keywords[code_str]:
                                product_keywords[code_str][kw] += count
                            else:
                                product_keywords[code_str][kw] = count
                    else:
                        product_keywords[code_str] = counts

    print(f"找到有关键词的商品编码数量: {len(product_keywords)}")
    if product_keywords:
        print(f"商品编码示例: {list(product_keywords.keys())[:5]}")

    # 如果没有找到有效的商品编码，则返回整体统计
    if not product_keywords:
        print("❗ 未找到有效商品编码或相关评论，将返回整体统计结果")
        return {"总体": count_keywords_overall(df["评论内容"], keywords)}

    return product_keywords


def count_keywords_overall(texts: pd.Series, keywords: list[str]) -> dict[str, int]:
    """统计词频；对大小写、全/半角、空格不敏感"""
    counts = {k: 0 for k in keywords}
    for t in texts:
        for kw in keywords:
            # 直接包含匹配；如需精确可换成正则 r"\b{}\b".format(re.escape(kw))
            if kw in t:
                counts[kw] += 1
    # 删除 0 次的关键词
    return {k: v for k, v in counts.items() if v > 0}


def plot_comprehensive_analysis(product_keywords: dict, top_n: int = 10):
    """绘制综合分析图表，包含堆叠柱状图、整体关键词统计和占比饼图"""
    if not product_keywords:
        print("❗ 未检测到任何关键词，请检查词表或评论内容")
        return

    # 处理JSON格式的商品编码，转换为更易读的格式
    cleaned_product_keywords = {}
    for product, kw_dict in product_keywords.items():
        if product.startswith('[{') and product.endswith('}]'):
            spec_value = extract_spec_value(product)
            if spec_value:
                # 如果已经存在相同规格值，合并关键词计数
                if spec_value in cleaned_product_keywords:
                    for kw, count in kw_dict.items():
                        if kw in cleaned_product_keywords[spec_value]:
                            cleaned_product_keywords[spec_value][kw] += count
                        else:
                            cleaned_product_keywords[spec_value][kw] = count
                else:
                    cleaned_product_keywords[spec_value] = kw_dict
        else:
            cleaned_product_keywords[product] = kw_dict

    # 使用清理后的商品编码数据
    product_keywords = cleaned_product_keywords

    # 提取所有唯一的关键词并计算总频次
    all_keywords = set()
    keyword_total = {}
    total_count = 0
    
    for product, kw_dict in product_keywords.items():
        all_keywords.update(kw_dict.keys())
        for kw, count in kw_dict.items():
            keyword_total[kw] = keyword_total.get(kw, 0) + count
            total_count += count

    # 按总频次排序取前N个关键词
    top_keywords = sorted(keyword_total.items(), key=lambda x: x[1], reverse=True)[:top_n]
    top_kw_names = [k for k, _ in top_keywords]

    # 按照关键词总数量排序，选取前8个商品编码
    product_totals = {}
    for product, kw_dict in product_keywords.items():
        product_totals[product] = sum(kw_dict.values())

    top_products = sorted(product_totals.items(), key=lambda x: x[1], reverse=True)
    max_products = min(8, len(top_products))  # 最多取8个商品编码
    top_product_names = [p for p, _ in top_products[:max_products]]

    # 准备堆叠柱状图数据
    data = np.zeros((len(top_kw_names), len(top_product_names)))

    for i, kw in enumerate(top_kw_names):
        for j, product in enumerate(top_product_names):
            if product in product_keywords and kw in product_keywords[product]:
                data[i, j] = product_keywords[product][kw]

    # 创建综合图表
    fig = plt.figure(figsize=(20, 12))
    
    # 子图1：堆叠柱状图
    ax1 = plt.subplot(2, 3, (1, 2))
    
    # 创建底部累积值
    bottoms = np.zeros(len(top_product_names))
    colors = plt.cm.Set3(np.linspace(0, 1, len(top_kw_names)))

    bars = []
    for i, kw in enumerate(top_kw_names):
        bar = ax1.bar(top_product_names, data[i], bottom=bottoms, label=kw, color=colors[i])
        bars.append(bar)
        bottoms += data[i]

    # 在柱子上添加数值标签
    for j, product in enumerate(top_product_names):
        y_pos = 0
        for i, kw in enumerate(top_kw_names):
            if data[i, j] > 0:
                ax1.text(j, y_pos + data[i, j]/2, f'{int(data[i, j])}', 
                        ha='center', va='center', fontsize=8, fontweight='bold')
                y_pos += data[i, j]
        # 添加总计标签
        ax1.text(j, y_pos + 1, f'总计:{int(y_pos)}', 
                ha='center', va='bottom', fontsize=9, fontweight='bold', color='red')

    ax1.set_title("各商品编码质量问题关键词分布", fontsize=14, fontweight='bold')
    ax1.set_xlabel("商品编码", fontsize=12)
    ax1.set_ylabel("关键词出现次数", fontsize=12)
    ax1.tick_params(axis='x', rotation=45)
    ax1.legend(title="关键词", bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)

    # 子图2：整体关键词柱状图
    ax2 = plt.subplot(2, 3, (4, 5))
    
    # 按频次排序，取前15个
    items = sorted(keyword_total.items(), key=lambda x: x[1], reverse=True)[:15]
    labels, values = zip(*items)
    
    bars2 = ax2.bar(labels, values, color=plt.cm.viridis(np.linspace(0, 1, len(labels))))
    
    # 在柱子上添加数值和占比标签
    for i, (bar, value) in enumerate(zip(bars2, values)):
        percentage = (value / total_count) * 100
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                f'{value}\n({percentage:.1f}%)', 
                ha='center', va='bottom', fontsize=8, fontweight='bold')

    ax2.set_title("整体质量问题关键词出现次数及占比", fontsize=14, fontweight='bold')
    ax2.set_ylabel("出现次数", fontsize=12)
    ax2.tick_params(axis='x', rotation=45)

    # 子图3：前5个关键词的饼图
    ax3 = plt.subplot(2, 3, 3)
    
    top5_items = sorted(keyword_total.items(), key=lambda x: x[1], reverse=True)[:5]
    top5_labels, top5_values = zip(*top5_items)
    
    # 计算占比
    top5_percentages = [(value / total_count) * 100 for value in top5_values]
    
    # 创建饼图
    wedges, texts, autotexts = ax3.pie(top5_values, labels=top5_labels, autopct='%1.1f%%', 
                                      startangle=90, colors=plt.cm.Pastel1(np.linspace(0, 1, 5)))
    
    ax3.set_title("前5个关键词占比分布", fontsize=14, fontweight='bold')

    # 子图4：商品编码问题统计
    ax4 = plt.subplot(2, 3, 6)
    
    # 统计各商品的问题总数
    product_issue_counts = [(product, sum(kw_dict.values())) for product, kw_dict in product_keywords.items()]
    product_issue_counts.sort(key=lambda x: x[1], reverse=True)
    
    top_issue_products = product_issue_counts[:8]
    product_names, issue_counts = zip(*top_issue_products)
    
    bars4 = ax4.barh(product_names, issue_counts, color=plt.cm.Oranges(np.linspace(0.3, 1, len(product_names))))
    
    # 添加数值标签
    for i, (bar, count) in enumerate(zip(bars4, issue_counts)):
        percentage = (count / total_count) * 100
        ax4.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2, 
                f'{count} ({percentage:.1f}%)', 
                ha='left', va='center', fontsize=9, fontweight='bold')

    ax4.set_title("各商品问题数量排名", fontsize=14, fontweight='bold')
    ax4.set_xlabel("问题总数", fontsize=12)

    # 添加整体统计信息
    fig.suptitle(f'质量问题关键词综合分析报告\n总问题数: {total_count} | 涉及商品: {len(product_keywords)} | 关键词类型: {len(keyword_total)}', 
                fontsize=16, fontweight='bold', y=0.98)

    plt.tight_layout()
    plt.subplots_adjust(top=0.92)

    # 保存图片
    save_dir = os.path.dirname(INPUT_FILE)
    save_path = os.path.join(save_dir, "comprehensive_quality_analysis.png")
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"📊 综合分析图表已保存至: {save_path}")

    plt.show()

    # 生成详细统计报告
    generate_detailed_report(product_keywords, keyword_total, total_count)


def extract_spec_value(json_str):
    """从JSON格式的商品规格中提取规格值"""
    try:
        specs = json.loads(json_str)
        # 我们假设规格格式为 [{"spec_key":"尺码","spec_value":"2双【白色小腿】"}]
        if isinstance(specs, list) and len(specs) > 0:
            for spec in specs:
                if "spec_value" in spec:
                    return spec["spec_value"]
        return None
    except Exception as e:
        # print(f"解析JSON字符串失败: {json_str} - {e}")
        return None


def generate_detailed_report(product_keywords: dict, keyword_total: dict, total_count: int):
    """生成详细的统计报告"""
    save_dir = os.path.dirname(INPUT_FILE) if os.path.dirname(INPUT_FILE) else "."
    report_path = os.path.join(save_dir, "detailed_quality_analysis_report.txt")
    
    with open(report_path, "w", encoding="utf-8") as f:
        file_name = os.path.basename(INPUT_FILE)
        f.write("="*80 + "\n")
        f.write(f"质量问题关键词详细分析报告\n")
        f.write(f"数据源: {file_name}\n")
        f.write(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*80 + "\n\n")
        
        # 总体统计信息
        f.write("📊 总体统计信息\n")
        f.write("-"*50 + "\n")
        f.write(f"总问题数量: {total_count}\n")
        f.write(f"涉及商品数量: {len(product_keywords)}\n")
        f.write(f"关键词类型数量: {len(keyword_total)}\n")
        f.write(f"平均每商品问题数: {total_count/len(product_keywords):.2f}\n\n")
        
        # 关键词排行榜
        f.write("🏆 关键词问题频次排行榜 (Top 20)\n")
        f.write("-"*50 + "\n")
        f.write(f"{'排名':<4} {'关键词':<15} {'出现次数':<8} {'占比':<8} {'累计占比'}\n")
        f.write("-"*50 + "\n")
        
        sorted_keywords = sorted(keyword_total.items(), key=lambda x: x[1], reverse=True)
        cumulative_percentage = 0
        
        for i, (keyword, count) in enumerate(sorted_keywords[:20], 1):
            percentage = (count / total_count) * 100
            cumulative_percentage += percentage
            f.write(f"{i:<4} {keyword:<15} {count:<8} {percentage:>6.1f}% {cumulative_percentage:>8.1f}%\n")
        
        f.write("\n")
        
        # 商品问题严重程度排行
        f.write("🚨 商品问题严重程度排行榜\n")
        f.write("-"*50 + "\n")
        f.write(f"{'排名':<4} {'商品编码':<25} {'问题总数':<8} {'占比':<8} {'严重程度'}\n")
        f.write("-"*50 + "\n")
        
        product_issue_counts = [(product, sum(kw_dict.values())) for product, kw_dict in product_keywords.items()]
        product_issue_counts.sort(key=lambda x: x[1], reverse=True)
        
        for i, (product, count) in enumerate(product_issue_counts, 1):
            percentage = (count / total_count) * 100
            if percentage >= 10:
                severity = "🔴 高"
            elif percentage >= 5:
                severity = "🟡 中"
            else:
                severity = "🟢 低"
            
            # 截断过长的商品编码
            display_product = product[:23] + "..." if len(product) > 25 else product
            f.write(f"{i:<4} {display_product:<25} {count:<8} {percentage:>6.1f}% {severity}\n")
        
        f.write("\n")
        
        # 各商品详细问题分析
        f.write("📋 各商品详细问题分析\n")
        f.write("="*80 + "\n")
        
        for i, (product, kw_dict) in enumerate(product_keywords.items(), 1):
            product_total = sum(kw_dict.values())
            f.write(f"\n{i}. 商品编码: {product}\n")
            f.write(f"   问题总数: {product_total} ({(product_total/total_count)*100:.1f}%)\n")
            f.write("   问题详情:\n")
            
            # 按频次排序该商品的关键词
            sorted_product_kws = sorted(kw_dict.items(), key=lambda x: x[1], reverse=True)
            
            for j, (kw, count) in enumerate(sorted_product_kws, 1):
                kw_percentage = (count / product_total) * 100
                overall_percentage = (count / total_count) * 100
                f.write(f"     {j:2d}. {kw:<15} {count:>3d}次 "
                       f"(商品内占比:{kw_percentage:>5.1f}% | 总体占比:{overall_percentage:>5.1f}%)\n")
        
        f.write("\n" + "="*80 + "\n")
        f.write("报告生成完成\n")
    
    print(f"📋 详细分析报告已保存至: {report_path}")


###############################################################################
# ▶️  入口
###############################################################################
def main():
    # 读取数据，返回DataFrame而不是Series
    print(f"正在处理文件: {INPUT_FILE}")

    # 检查文件路径是否正确
    if not os.path.exists(INPUT_FILE):
        print(f"❗ 文件不存在: {INPUT_FILE}")
        # 尝试在当前目录下查找可能的文件
        files = [f for f in os.listdir() if f.endswith('.xlsx') or f.endswith('.csv')]
        if files:
            print(f"当前目录下的可用文件: {files}")
            # 使用找到的第一个文件
            new_file = files[0]
            print(f"将使用文件: {new_file}")
            # 不使用global声明，直接在函数内使用INPUT_FILE
            df = load_reviews(new_file)
    else:
        df = load_reviews(INPUT_FILE)

    print(f"读取到的数据行数: {len(df)}")

    # 按商品编码分组统计关键词
    product_kw_freq = count_keywords_by_product(df, QUALITY_KWS)

    # 输出和保存结果
    save_dir = os.path.dirname(INPUT_FILE) if os.path.dirname(INPUT_FILE) else "."
    save_path = os.path.join(save_dir, "product_code_keywords_count.txt")

    with open(save_path, "w", encoding="utf-8") as f:
        # 修复文件路径分割问题
        file_name = os.path.basename(INPUT_FILE)
        f.write(f"{file_name} === 商品编码关键词统计 ===\n\n")

        for product, kw_dict in product_kw_freq.items():
            # 如果商品编码是JSON格式，提取规格值
            product_display = product
            if product.startswith('[{') and product.endswith('}]'):
                spec_value = extract_spec_value(product)
                if spec_value:
                    product_display = spec_value

            f.write(f"== 商品编码: {product_display} ==\n")
            for k, v in sorted(kw_dict.items(), key=lambda x: x[1], reverse=True):
                f.write(f"{k:<8s}: {v:>4d}\n")
            f.write("\n")

    print(f"📄 商品编码关键词统计已保存至: {save_path}")

    # 绘制综合分析图表
    plot_comprehensive_analysis(product_kw_freq)


if __name__ == "__main__":
    main()
