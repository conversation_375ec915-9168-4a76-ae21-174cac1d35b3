import csv
import os
import json
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import as_completed
import requests
import pandas as pd
from datetime import datetime
import math


json_data = requests.get('http://121.43.34.187:9000/api/data1').json().get('data')
def get_cookie_from_cookie3(shop_name):
    if shop_name in json_data:
        cookie_list = json_data[shop_name]
        if isinstance(cookie_list, list):
            return '; '.join(cookie_list)
        elif isinstance(cookie_list, str):
            return cookie_list
        else:
            raise ValueError(f"Unexpected type for cookie: {type(cookie_list)}")
    return None

def fetch_balance_info(shop_name, page_num=1, page_size=50):
    cookie = get_cookie_from_cookie3(shop_name)
    if not cookie:
        print(f"无法获取 {shop_name} 的cookie")
        return None
    
    aas = requests.get('http://121.43.34.187:9000/api/0as').json().get('data')
        
    headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'anti-content': aas,
        'cache-control': 'max-age=0',
        'content-type': 'application/json',
        'etag': 'Ki2bBWp1fmllOyCwpJYpOiQZsY7DZ6Ch',
        'origin': 'https://mms.pinduoduo.com',
        'priority': 'u=1, i',
        'referer': 'https://mms.pinduoduo.com/mms-marketing-mixin/price-violation/problem-orders?goods_id=485857593913',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        'cookie': cookie,
    }
    
    json_data = {
        'page_num': page_num,
        'page_size': page_size,
        # 'goods_ids': [
        #     713033303076,
        # ],
    }
    
    try:
        response = requests.post(
            'https://mms.pinduoduo.com/api/price/mariana/quality_experience/order_detail_list',
            headers=headers,
            json=json_data,
            timeout=30
        )
        
        if response.status_code == 200:
            return {
                'shop_name': shop_name,
                'data': response.json()
            }
        else:
            print(f"请求失败 {shop_name}: 状态码 {response.status_code}")
            return None
    except Exception as e:
        print(f"请求出错 {shop_name}: {str(e)}")
        return None

def fetch_all_pages(shop_name):
    print(f"开始获取 {shop_name} 的所有页数据...")
    page_num = 1
    page_size = 50
    all_shop_data = []
    
    # 获取第一页数据
    first_page = fetch_balance_info(shop_name, page_num, page_size)
    if not first_page or 'data' not in first_page or not first_page['data'].get('success'):
        print(f"无法获取 {shop_name} 的第一页数据")
        return []
    
    result = first_page['data'].get('result', {})
    total_items = result.get('total', 0)
    all_shop_data.append(first_page)
    
    # 计算总页数
    total_pages = math.ceil(total_items / page_size)
    print(f"{shop_name} 共有 {total_items} 条数据，{total_pages} 页")
    
    # 获取剩余页数据
    for page in range(2, total_pages + 1):
        print(f"正在获取 {shop_name} 第 {page}/{total_pages} 页...")
        page_data = fetch_balance_info(shop_name, page, page_size)
        if page_data and 'data' in page_data and page_data['data'].get('success'):
            all_shop_data.append(page_data)
        else:
            print(f"获取 {shop_name} 第 {page} 页数据失败")
    
    return all_shop_data

def save_to_excel(all_data):
    # 准备主表数据
    main_rows = []
    # 准备问题详情表数据
    detail_rows = []
    
    for shop_data_list in all_data:
        for shop_data in shop_data_list:
            if not shop_data or 'data' not in shop_data:
                continue
                
            shop_name = shop_data['shop_name']
            response_data = shop_data['data']
            
            if not response_data.get('success'):
                print(f"获取 {shop_name} 的数据失败")
                continue
                
            result = response_data.get('result', {})
            items = result.get('list', [])
            
            for item in items:
                # 基本信息
                row = {
                    '店铺名称': shop_name,
                    '商品ID': item.get('goods_id'),
                    '商品名称': item.get('goods_name'),
                    '图片链接': item.get('img_url'),
                    'SKU名称': item.get('sku_name'),
                    'SKU ID': item.get('sku_id'),
                    '订单编号': item.get('order_sn'),
                    '订单时间': datetime.fromtimestamp(item.get('order_time', 0)/1000).strftime('%Y-%m-%d %H:%M:%S') if item.get('order_time') else '',
                    '订单金额': item.get('order_amount', 0) / 100 if item.get('order_amount') is not None else '',  # 转换为元
                    '是否支持申诉': '是' if item.get('support_appeal') else '否',
                    '申诉结束时间': datetime.fromtimestamp(item.get('appeal_end_time', 0)).strftime('%Y-%m-%d %H:%M:%S') if item.get('appeal_end_time') else '',
                    '问题类型': item.get('dimension_out', ''),
                    '问题聚类描述': item.get('goods_problem_clustering_result', {}).get('goods_clustering_desc', '')
                }
                
                # 处理问题详情
                problem_details = item.get('goods_problem_clustering_result', {}).get('goods_problem_detail_list', [])
                problem_messages = []
                
                for detail in problem_details:
                    problem_messages.append(f"{detail.get('order_sn')}: {detail.get('chat_msg')}")
                    
                    # 为问题详情表添加一行
                    detail_row = {
                        '店铺名称': shop_name,
                        '商品ID': item.get('goods_id'),
                        '商品名称': item.get('goods_name'),
                        'SKU名称': item.get('sku_name'),
                        'SKU ID': item.get('sku_id'),
                        '订单编号': item.get('order_sn'),
                        '问题类型': item.get('dimension_out', ''),
                        '问题聚类描述': item.get('goods_problem_clustering_result', {}).get('goods_clustering_desc', ''),
                        '评论订单编号': detail.get('order_sn'),
                        '评论内容': detail.get('chat_msg')
                    }
                    detail_rows.append(detail_row)
                    
                row['问题详情'] = '\n'.join(problem_messages)
                main_rows.append(row)
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'output')
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成文件名基础
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    
    # 创建主表DataFrame并保存
    if main_rows:
        main_df = pd.DataFrame(main_rows)
        
        # 设置列顺序
        main_columns = [
            '店铺名称', '商品ID', '商品名称', 'SKU名称', 'SKU ID', 
            '订单编号', '订单时间', '订单金额', '是否支持申诉', 
            '申诉结束时间', '问题类型', '问题聚类描述', '问题详情', '图片链接'
        ]
        
        main_df = main_df[main_columns]
        
        # 保存主表Excel
        main_output_file = os.path.join(output_dir, f'商品评论数据_{timestamp}.xlsx')
        main_df.to_excel(main_output_file, index=False)
        print(f"主表数据已保存到: {main_output_file}")
    else:
        print("没有主表数据可保存")
        main_output_file = None
    
    # 创建详情表DataFrame并保存
    if detail_rows:
        detail_df = pd.DataFrame(detail_rows)
        
        # 设置列顺序
        detail_columns = [
            '店铺名称', '商品ID', '商品名称', 'SKU名称', 'SKU ID', 
            '订单编号', '问题类型', '问题聚类描述', '评论订单编号', '评论内容'
        ]
        
        detail_df = detail_df[detail_columns]
        
        # 保存详情表Excel
        detail_output_file = os.path.join(output_dir, f'商品评论详情数据_{timestamp}.xlsx')
        detail_df.to_excel(detail_output_file, index=False)
        print(f"详情表数据已保存到: {detail_output_file}")
    else:
        print("没有详情表数据可保存")
        detail_output_file = None
    
    return main_output_file, detail_output_file

# 主函数：并发获取所有店铺的评论信息
if __name__ == "__main__":
    # 读取店铺名称列表
    shop_account_file_path = os.path.join('F:\李泽鹏\获取数据', '店铺-账号信息.csv')
    
    try:
        with open(shop_account_file_path, mode='r', encoding='ANSI') as shop_file:
            reader = csv.reader(shop_file)
            next(reader)  # 跳过表头
            shop_names = [row[2] for row in reader]  # 获取第三列店铺名称
    except Exception as e:
        print(f"读取店铺文件出错: {str(e)}")
        shop_names = []
    
    if not shop_names:
        print("没有找到店铺名称，将使用测试店铺")
        shop_names = ["测试店铺"]  # 可以替换为实际的测试店铺
    
    print(f"开始获取 {len(shop_names)} 个店铺的评论数据...")
    
    # 存储所有店铺的数据
    all_data = []
    
    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_shop = {
            executor.submit(fetch_all_pages, shop_name): shop_name
            for shop_name in shop_names
        }
        
        for future in as_completed(future_to_shop):
            shop_name = future_to_shop[future]
            try:
                data = future.result()
                if data:
                    all_data.append(data)
                    print(f"已获取 {shop_name} 的所有数据")
            except Exception as e:
                print(f"处理 {shop_name} 的数据时出错: {str(e)}")
    
    # 保存所有数据到Excel
    main_output_file, detail_output_file = save_to_excel(all_data)
    
    if main_output_file or detail_output_file:
        print(f"所有数据已保存")
    else:
        print("未能保存数据")

#返回示例
# {
#     "success": true,
#     "error_code": 1000000,
#     "result": {
#         "total": 1053,
#         "list": [
#             {
#                 "goods_id": 713033303076,
#                 "goods_name": "奶白色天鹅绒微压jk过膝袜春夏薄款防滑防掉纯欲风中长筒小腿袜女",
#                 "img_url": "https://img.pddpic.com/gaudit-image/2025-03-08/43bdcfb45f32cc417b0409927ff4ef26.jpeg",
#                 "sku_name": "（过膝袜】1双-,奶白色天鹅绒】-微压显瘦",
#                 "sku_id": 1709965105419,
#                 "order_sn": "250518-155252170320271",
#                 "order_time": 1747541951000,
#                 "order_amount": 596,
#                 "support_appeal": true,
#                 "appeal_end_time": 1750243245,
#                 "dimension_out": "商品差评",
#                 "goods_problem_clustering_result": {
#                     "goods_clustering_desc": "NORMAL",
#                     "goods_problem_detail_list": [
#
#                     ]
#                 }
#             },
#             {
#                 "goods_id": 713033303076,
#                 "goods_name": "奶白色天鹅绒微压jk过膝袜春夏薄款防滑防掉纯欲风中长筒小腿袜女",
#                 "img_url": "https://img.pddpic.com/gaudit-image/2025-03-08/43bdcfb45f32cc417b0409927ff4ef26.jpeg",
#                 "sku_name": "（小腿袜】1双-,奶白色天鹅绒】-微压显瘦",
#                 "sku_id": 1709965105417,
#                 "order_sn": "250517-132865072770607",
#                 "order_time": 1747479399000,
#                 "order_amount": 546,
#                 "support_appeal": true,
#                 "appeal_end_time": 1750337136,
#                 "dimension_out": "商品问题",
#                 "goods_problem_clustering_result": {
#                     "goods_clustering_desc": "CLUSTERING_LOW",
#                     "goods_problem_detail_list": [
#                         {
#                             "order_sn": "250504-063963153934011",
#                             "chat_msg": "这个袜子好皱#打开一看皱皱巴巴的#"
#                         },
#                         {
#                             "order_sn": "250413-520015133020772",
#                             "chat_msg": "像别人穿过的很褶皱而且一股味#"
#                         },
#                         {
#                             "order_sn": "250511-640679964313045",
#                             "chat_msg": "皱巴巴的#"
#                         },
#                         {
#                             "order_sn": "250509-382499602801354",
#                             "chat_msg": "这么皱，确定不是被穿过的吗#"
#                         },
#                         {
#                             "order_sn": "250517-349679142502738",
#                             "chat_msg": "都皱成什么了#"
#                         },
#                         {
#                             "order_sn": "250507-291934051913794",
#                             "chat_msg": "短的那双明显有很多褶皱，明显是被人穿过了#我等了好久的新袜子总是这种皱了吧唧#"
#                         },
#                         {
#                             "order_sn": "250518-414496867981885",
#                             "chat_msg": "这么软都全皱的？#"
#                         },
#                         {
#                             "order_sn": "250506-425187110413940",
#                             "chat_msg": "太褶皱了#"
#                         },
#                         {
#                             "order_sn": "250517-430314647071379",
#                             "chat_msg": "刚拿来那上面特别皱#"
#                         },
#                         {
#                             "order_sn": "250510-206957473393688",
#                             "chat_msg": "和描述的不一样而且也很次很皱#"
#                         },
#                         {
#                             "order_sn": "250512-075602357772394",
#                             "chat_msg": "为什么我收到这么皱皱巴巴的#就图片上也是光滑平整的 我收到就皱皱巴巴的#"
#                         },
#                         {
#                             "order_sn": "250515-231132382792073",
#                             "chat_msg": "我的袜子特别皱#"
#                         },
#                         {
#                             "order_sn": "250511-121184008620392",
#                             "chat_msg": "褶皱这么多#"
#                         },
#                         {
#                             "order_sn": "250516-377120366341320",
#                             "chat_msg": "皱的不行#"
#                         },
#                         {
#                             "order_sn": "250427-456534269501927",
#                             "chat_msg": "这为什么皱皱巴巴的#我买这么多袜子第一次见发过来是皱的#"
#                         },
#                         {
#                             "order_sn": "250509-424987860790781",
#                             "chat_msg": "皱成这样#"
#                         },
#                         {
#                             "order_sn": "250516-240165854732065",
#                             "chat_msg": "真的很皱#第一眼收过来的时候，我还以为我买的什么东西皱皱巴巴#你看看你们自己家评论区的其他袜子都很平整，为什么发过来就有的皱皱巴巴？#皱巴巴的不喜欢#"
#                         },
#                         {
#                             "order_sn": "250507-297806108290408",
#                             "chat_msg": "这也太皱了吧，这不是别人穿了退货发给我的吧#怎么会有新的这么皱，而且还有脏东西#谁的新东西会有这么皱巴巴的，我真是服了#"
#                         },
#                         {
#                             "order_sn": "250502-529918902640147",
#                             "chat_msg": "我不喜欢你们家袜子的材质，每一双都皱巴巴叭#"
#                         },
#                         {
#                             "order_sn": "250427-654940639561562",
#                             "chat_msg": "的褶皱#"
#                         },
#                         {
#                             "order_sn": "250502-122148625941996",
#                             "chat_msg": "褶皱太多了#"
#                         }
#                     ]
#                 }
#             }
#         ]
#     }
# }